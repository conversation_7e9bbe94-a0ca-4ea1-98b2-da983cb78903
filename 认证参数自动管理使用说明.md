# Temu认证参数自动管理解决方案

## 问题描述

在使用多店铺待发货订单导出工具时，经常遇到403错误，这是因为`seller_temp`等认证参数会定期过期。之前需要手动复制新的参数，现在通过自动管理系统解决这个问题。

## 解决方案

### 1. 新增认证参数管理器 (`认证参数管理器.py`)

这个模块提供以下功能：

- **自动检测参数过期**：通过解码`seller_temp`参数中的时间戳判断是否过期
- **自动刷新认证参数**：当参数过期时，自动访问店铺页面获取新的认证参数
- **智能缓存机制**：缓存有效的认证参数，避免频繁刷新
- **参数有效性测试**：通过实际API调用测试认证参数是否有效

### 2. 增强的多店铺导出工具

修改了`多店铺待发货订单导出工具.py`，集成认证参数管理器：

- **自动重试机制**：检测到403错误时自动重试
- **智能参数刷新**：使用认证管理器自动获取新的认证参数
- **错误处理优化**：更好地处理认证失败的情况

## 使用方法

### 1. 启动Chrome调试模式

```bash
chrome.exe --remote-debugging-port=9337
```

### 2. 在浏览器中登录

确保在Chrome浏览器中已经登录到Temu卖家后台：
- 访问 `https://seller.kuajingmaihuo.com`
- 完成登录流程

### 3. 运行导出工具

```bash
python 多店铺待发货订单导出工具.py
```

## 工作原理

### 认证参数生命周期管理

1. **初始获取**：从浏览器cookies中获取认证参数
2. **有效性检查**：解码`seller_temp`参数，检查时间戳
3. **自动刷新**：当参数过期时，自动访问店铺页面获取新参数
4. **缓存管理**：将有效参数缓存30分钟，避免频繁刷新

### seller_temp参数解析

`seller_temp`参数格式：`N_<base64编码的JSON>`

解码后的JSON结构：
```json
{
  "t": "加密的token数据",
  "v": 1,
  "s": 时间戳,
  "u": 用户ID
}
```

### 自动重试机制

当遇到403错误时：
1. 第一次重试：清除缓存，获取新的认证参数
2. 第二次重试：如果仍然失败，标记为最终失败

## 主要改进

### 1. 无需手动复制参数

- ✅ 自动从浏览器获取最新认证参数
- ✅ 智能检测参数是否过期
- ✅ 自动刷新过期的参数

### 2. 更好的错误处理

- ✅ 识别403认证错误
- ✅ 自动重试机制
- ✅ 详细的错误日志

### 3. 性能优化

- ✅ 认证参数缓存机制
- ✅ 避免不必要的参数刷新
- ✅ 并发处理多个店铺

## 故障排除

### 常见问题

1. **仍然出现403错误**
   - 检查浏览器是否正确登录
   - 确认Chrome调试端口是否正确
   - 尝试手动访问店铺页面确认权限

2. **认证参数获取失败**
   - 检查网络连接
   - 确认浏览器cookies是否完整
   - 重新登录浏览器

3. **部分店铺无法访问**
   - 检查店铺权限
   - 确认店铺ID是否正确
   - 尝试手动访问店铺页面

### 调试模式

可以单独运行认证参数管理器进行测试：

```bash
python 认证参数管理器.py
```

这将测试认证参数的获取和验证功能。

## 技术细节

### 认证参数类型

1. **AccessToken**：API访问令牌
2. **seller_temp**：临时会话令牌（包含时间戳）
3. **user_uin**：用户标识
4. **mallid**：店铺ID

### 缓存策略

- **缓存时间**：30分钟
- **缓存键**：`{mall_id}_{domain}`
- **自动清理**：过期自动清理

### 安全考虑

- 认证参数仅在内存中缓存
- 不会持久化敏感信息
- 使用HTTPS确保传输安全

## 更新日志

### v2.0 (当前版本)
- 新增认证参数管理器
- 自动检测和刷新过期参数
- 智能缓存机制
- 增强错误处理

### v1.0 (原版本)
- 基础多店铺导出功能
- 手动配置认证参数
- 简单错误处理

## 联系支持

如果遇到问题，请提供以下信息：
1. 错误日志
2. 店铺ID
3. 浏览器版本
4. 操作系统信息
