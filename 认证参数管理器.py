#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu认证参数自动管理器
功能：
1. 自动获取和刷新认证参数
2. 检测认证参数是否过期
3. 提供统一的认证参数接口
"""

import time
import json
import requests
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from datetime import datetime, timedelta
import base64


class TemuAuthManager:
    def __init__(self, driver=None):
        """
        初始化认证管理器
        
        Args:
            driver: Selenium WebDriver实例，如果为None则创建新的
        """
        self.driver = driver
        self.wait = None
        if self.driver:
            self.wait = WebDriverWait(self.driver, 10)
        
        # 认证参数缓存
        self.auth_cache = {}
        self.cache_expiry = {}
        
        # 默认的认证参数过期时间（分钟）
        self.default_expiry_minutes = 30
        
    def decode_seller_temp(self, seller_temp):
        """
        解码seller_temp参数，检查是否过期
        
        Args:
            seller_temp: seller_temp参数值
            
        Returns:
            dict: 解码后的信息，包含过期时间等
        """
        try:
            if seller_temp.startswith('N_'):
                # 去掉前缀
                encoded_data = seller_temp[2:]
                
                # Base64解码
                decoded_bytes = base64.b64decode(encoded_data)
                decoded_str = decoded_bytes.decode('utf-8')
                
                # 解析JSON
                data = json.loads(decoded_str)
                
                return {
                    'success': True,
                    'data': data,
                    'user_id': data.get('u'),
                    'timestamp': data.get('s'),
                    'version': data.get('v')
                }
        except Exception as e:
            print(f"解码seller_temp失败: {e}")
            
        return {'success': False}
    
    def is_auth_expired(self, cookies_dict):
        """
        检查认证参数是否过期
        
        Args:
            cookies_dict: cookies字典
            
        Returns:
            bool: True表示已过期，False表示未过期
        """
        seller_temp = cookies_dict.get('seller_temp', '')
        if not seller_temp:
            return True
            
        # 解码seller_temp检查时间戳
        decode_result = self.decode_seller_temp(seller_temp)
        if not decode_result['success']:
            return True
            
        # 检查时间戳（假设时间戳是毫秒级）
        timestamp = decode_result.get('timestamp', 0)
        if timestamp:
            try:
                # 转换为秒级时间戳
                if timestamp > 10**12:  # 毫秒级时间戳
                    timestamp = timestamp / 1000
                    
                token_time = datetime.fromtimestamp(timestamp)
                current_time = datetime.now()
                
                # 如果token超过30分钟，认为过期
                if current_time - token_time > timedelta(minutes=self.default_expiry_minutes):
                    print(f"认证参数已过期，token时间: {token_time}, 当前时间: {current_time}")
                    return True
                    
            except Exception as e:
                print(f"检查时间戳失败: {e}")
                return True
                
        return False
    
    def get_fresh_cookies(self, mall_id, domain="https://agentseller-us.temu.com"):
        """
        获取新鲜的认证参数
        
        Args:
            mall_id: 店铺ID
            domain: 域名
            
        Returns:
            dict: 新的cookies字典
        """
        if not self.driver:
            print("❌ 未提供WebDriver实例，无法获取认证参数")
            return None
            
        try:
            print(f"🔄 正在获取店铺 {mall_id} 的新认证参数...")
            
            # 访问店铺页面
            url = f"{domain}/mmsos/orders.html?init=true&mallId={mall_id}"
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 检查是否需要登录
            current_url = self.driver.current_url
            if "login" in current_url.lower():
                print("❌ 需要登录，请先在浏览器中登录")
                return None
                
            # 获取cookies
            selenium_cookies = self.driver.get_cookies()
            cookies_dict = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
            
            # 验证关键参数是否存在
            required_params = ['AccessToken', 'seller_temp']
            missing_params = [param for param in required_params if not cookies_dict.get(param)]
            
            if missing_params:
                print(f"❌ 缺少关键认证参数: {missing_params}")
                return None
                
            # 缓存认证参数
            cache_key = f"{mall_id}_{domain}"
            self.auth_cache[cache_key] = cookies_dict
            self.cache_expiry[cache_key] = datetime.now() + timedelta(minutes=self.default_expiry_minutes)
            
            print(f"✅ 成功获取店铺 {mall_id} 的认证参数")
            return cookies_dict
            
        except Exception as e:
            print(f"❌ 获取认证参数失败: {e}")
            return None
    
    def get_valid_cookies(self, mall_id, current_cookies=None, domain="https://agentseller-us.temu.com"):
        """
        获取有效的认证参数，如果当前参数过期则自动刷新
        
        Args:
            mall_id: 店铺ID
            current_cookies: 当前的cookies字典
            domain: 域名
            
        Returns:
            dict: 有效的cookies字典
        """
        cache_key = f"{mall_id}_{domain}"
        
        # 检查缓存
        if (cache_key in self.auth_cache and 
            cache_key in self.cache_expiry and 
            datetime.now() < self.cache_expiry[cache_key]):
            print(f"✅ 使用缓存的认证参数 (店铺: {mall_id})")
            return self.auth_cache[cache_key]
        
        # 检查当前cookies是否有效
        if current_cookies and not self.is_auth_expired(current_cookies):
            print(f"✅ 当前认证参数仍然有效 (店铺: {mall_id})")
            # 更新缓存
            self.auth_cache[cache_key] = current_cookies
            self.cache_expiry[cache_key] = datetime.now() + timedelta(minutes=self.default_expiry_minutes)
            return current_cookies
        
        # 获取新的认证参数
        print(f"🔄 当前认证参数已过期，正在刷新... (店铺: {mall_id})")
        return self.get_fresh_cookies(mall_id, domain)
    
    def test_auth_validity(self, mall_id, cookies_dict, domain="https://agentseller-us.temu.com"):
        """
        测试认证参数的有效性
        
        Args:
            mall_id: 店铺ID
            cookies_dict: cookies字典
            domain: 域名
            
        Returns:
            bool: True表示有效，False表示无效
        """
        try:
            # 构造测试请求
            headers = {
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9",
                "content-type": "application/json;charset=UTF-8",
                "mallid": str(mall_id),
                "origin": domain,
                "referer": f"{domain}/mmsos/orders.html",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
            }
            
            url = f"{domain}/kirogi/bg/mms/recentOrderList"
            data = {
                "fulfillmentMode": 0,
                "pageNumber": 1,
                "pageSize": 1,
                "queryType": 2,
                "sortType": 1,
                "timeZone": "UTC+8",
                "parentAfterSalesTag": 0,
                "needBuySignService": 0,
                "sellerNoteLabelList": []
            }
            
            response = requests.post(url, headers=headers, cookies=cookies_dict, 
                                   json=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ 认证参数测试通过 (店铺: {mall_id})")
                    return True
                    
            print(f"❌ 认证参数测试失败 (店铺: {mall_id}), 状态码: {response.status_code}")
            return False
            
        except Exception as e:
            print(f"❌ 认证参数测试异常 (店铺: {mall_id}): {e}")
            return False
    
    def clear_cache(self, mall_id=None):
        """
        清除认证参数缓存
        
        Args:
            mall_id: 店铺ID，如果为None则清除所有缓存
        """
        if mall_id:
            # 清除特定店铺的缓存
            keys_to_remove = [key for key in self.auth_cache.keys() if key.startswith(f"{mall_id}_")]
            for key in keys_to_remove:
                self.auth_cache.pop(key, None)
                self.cache_expiry.pop(key, None)
            print(f"✅ 已清除店铺 {mall_id} 的认证参数缓存")
        else:
            # 清除所有缓存
            self.auth_cache.clear()
            self.cache_expiry.clear()
            print("✅ 已清除所有认证参数缓存")


def main():
    """测试函数"""
    print("=== Temu认证参数管理器测试 ===")
    
    # 连接Chrome浏览器
    chrome_options = webdriver.ChromeOptions()
    chrome_options.debugger_address = "127.0.0.1:9337"
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        auth_manager = TemuAuthManager(driver)
        
        # 测试店铺ID
        test_mall_id = "634418218259845"
        
        # 获取认证参数
        cookies = auth_manager.get_valid_cookies(test_mall_id)
        
        if cookies:
            print("✅ 成功获取认证参数")
            
            # 测试认证参数有效性
            is_valid = auth_manager.test_auth_validity(test_mall_id, cookies)
            print(f"认证参数有效性: {'有效' if is_valid else '无效'}")
            
            # 显示关键参数
            print(f"AccessToken: {cookies.get('AccessToken', 'N/A')[:20]}...")
            print(f"seller_temp: {cookies.get('seller_temp', 'N/A')[:50]}...")
        else:
            print("❌ 获取认证参数失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        try:
            driver.quit()
        except:
            pass


if __name__ == "__main__":
    main()
